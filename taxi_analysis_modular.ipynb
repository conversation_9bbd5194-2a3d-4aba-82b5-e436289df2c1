# Импорт необходимых библиотек
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import warnings
from typing import Dict, List, Optional, Union, Tuple, Any
from pathlib import Path
from scipy import stats

# Настройка отображения
plt.style.use('default')
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 10
warnings.filterwarnings('ignore', category=FutureWarning)

print("Библиотеки успешно импортированы")

def detect_file_type(file_path: str) -> str:
    """
    Определяет тип файла по расширению.
    
    Args:
        file_path: Путь к файлу
        
    Returns:
        str: Тип файла ('excel', 'csv', 'json')
        
    Raises:
        ValueError: Если расширение не поддерживается
    """
    path = Path(file_path)
    suffix = path.suffix.lower()
    
    if suffix in ['.xlsx', '.xls']:
        return 'excel'
    elif suffix == '.csv':
        return 'csv'
    elif suffix == '.json':
        return 'json'
    else:
        raise ValueError(f"Неподдерживаемое расширение файла: {suffix}")

def load_data_file(file_path: str, file_type: Optional[str] = None, **kwargs) -> pd.DataFrame:
    """
    Загружает данные из файла.
    
    Args:
        file_path: Путь к файлу
        file_type: Тип файла (определяется автоматически, если None)
        **kwargs: Дополнительные параметры для pandas
        
    Returns:
        pd.DataFrame: Загруженные данные
        
    Raises:
        FileNotFoundError: Если файл не найден
        ValueError: При ошибках загрузки
    """
    if not Path(file_path).exists():
        raise FileNotFoundError(f"Файл не найден: {file_path}")
    
    if file_type is None:
        file_type = detect_file_type(file_path)
    
    print(f"Загрузка {file_type} файла: {file_path}")
    
    try:
        if file_type == 'excel':
            df = pd.read_excel(file_path, **kwargs)
        elif file_type == 'csv':
            df = pd.read_csv(file_path, **kwargs)
        elif file_type == 'json':
            df = pd.read_json(file_path, **kwargs)
        else:
            raise ValueError(f"Неподдерживаемый тип файла: {file_type}")
            
        print(f"✓ Загружено {len(df)} строк и {len(df.columns)} колонок")
        return df
        
    except Exception as e:
        raise ValueError(f"Ошибка при загрузке файла: {str(e)}")

def validate_required_columns(df: pd.DataFrame, required_columns: List[str]) -> None:
    """
    Проверяет наличие обязательных колонок в DataFrame.
    
    Args:
        df: DataFrame для проверки
        required_columns: Список обязательных колонок
        
    Raises:
        ValueError: Если отсутствуют обязательные колонки
    """
    missing_columns = set(required_columns) - set(df.columns)
    
    if missing_columns:
        raise ValueError(f"Отсутствуют обязательные колонки: {missing_columns}")
    
    print(f"✓ Все обязательные колонки присутствуют: {required_columns}")

def check_duplicates(df: pd.DataFrame, id_column: str = 'id_order') -> None:
    """
    Проверяет наличие дубликатов в колонке ID.
    
    Args:
        df: DataFrame для проверки
        id_column: Название колонки с ID
    """
    if id_column not in df.columns:
        print(f"⚠ Колонка {id_column} не найдена")
        return
    
    duplicates = df[id_column].duplicated().sum()
    
    if duplicates > 0:
        warnings.warn(f"Обнаружено {duplicates} дублированных ID")
    else:
        print(f"✓ Дубликаты в колонке {id_column} не обнаружены")

def analyze_missing_values(df: pd.DataFrame, threshold: float = 50.0) -> Dict[str, float]:
    """
    Анализирует пропущенные значения в DataFrame.
    
    Args:
        df: DataFrame для анализа
        threshold: Порог в процентах для предупреждения
        
    Returns:
        Dict[str, float]: Статистика пропущенных значений по колонкам
    """
    missing_stats = df.isnull().sum()
    missing_percent = (missing_stats / len(df)) * 100
    
    result = {}
    
    if missing_stats.sum() == 0:
        print("✓ Пропущенные значения не обнаружены")
        return result
    
    print("\nСтатистика пропущенных значений:")
    for col, count in missing_stats[missing_stats > 0].items():
        percent = missing_percent[col]
        result[col] = percent
        
        status = "⚠" if percent > threshold else "ℹ"
        print(f"  {status} {col}: {count} ({percent:.1f}%)")
        
        if percent > threshold:
            warnings.warn(f"Колонка {col} содержит более {threshold}% пропущенных значений")
    
    return result

def convert_time_column(series: pd.Series, 
                       time_format: str = "%d.%m.%y %H:%M",
                       column_name: str = "column") -> pd.Series:
    """
    Преобразует одну колонку в формат datetime.
    
    Args:
        series: Серия для преобразования
        time_format: Формат времени
        column_name: Название колонки (для логирования)
        
    Returns:
        pd.Series: Преобразованная серия
    """
    if series.dtype == 'datetime64[ns]':
        print(f"✓ Колонка {column_name} уже в формате datetime")
        return series
    
    # Попытка преобразования с заданным форматом
    try:
        converted = pd.to_datetime(series, format=time_format)
        print(f"✓ Колонка {column_name} преобразована с форматом {time_format}")
        return converted
    except Exception as e:
        print(f"⚠ Ошибка преобразования {column_name} с форматом {time_format}: {e}")
    
    # Попытка автоматического определения формата
    try:
        converted = pd.to_datetime(series, errors='coerce')
        success_rate = (1 - converted.isna().mean()) * 100
        
        if success_rate > 50:
            print(f"✓ Колонка {column_name} преобразована автоматически (успешность: {success_rate:.1f}%)")
            return converted
        else:
            print(f"✗ Низкая успешность автоматического преобразования {column_name}: {success_rate:.1f}%")
            return series
    except Exception:
        print(f"✗ Не удалось преобразовать колонку {column_name}")
        return series

def convert_time_columns(df: pd.DataFrame, 
                        time_columns: List[str],
                        time_format: str = "%d.%m.%y %H:%M") -> pd.DataFrame:
    """
    Преобразует несколько временных колонок.
    
    Args:
        df: DataFrame с данными
        time_columns: Список колонок для преобразования
        time_format: Формат времени
        
    Returns:
        pd.DataFrame: DataFrame с преобразованными колонками
    """
    df_result = df.copy()
    
    print(f"Преобразование временных колонок: {time_columns}")
    
    for col in time_columns:
        if col in df_result.columns:
            df_result[col] = convert_time_column(df_result[col], time_format, col)
        else:
            print(f"⚠ Колонка {col} не найдена в данных")
    
    return df_result

def add_time_derived_columns(df: pd.DataFrame, 
                           base_column: str = 'order_time') -> pd.DataFrame:
    """
    Добавляет производные временные колонки.
    
    Args:
        df: DataFrame с данными
        base_column: Базовая временная колонка
        
    Returns:
        pd.DataFrame: DataFrame с добавленными колонками
    """
    df_result = df.copy()
    
    if base_column not in df_result.columns:
        print(f"⚠ Базовая колонка {base_column} не найдена")
        return df_result
    
    if df_result[base_column].dtype != 'datetime64[ns]':
        print(f"⚠ Колонка {base_column} не в формате datetime")
        return df_result
    
    # Добавляем производные колонки
    df_result['day_order'] = df_result[base_column].dt.day
    df_result['hour_order'] = df_result[base_column].dt.floor('h')
    df_result['weekday'] = df_result[base_column].dt.dayofweek
    df_result['month'] = df_result[base_column].dt.month
    
    print("✓ Добавлены производные колонки: day_order, hour_order, weekday, month")
    return df_result

def create_aggregation_config(df: pd.DataFrame, 
                             metrics_config: Optional[Dict[str, str]] = None) -> Dict[str, Tuple[str, str]]:
    """
    Создает конфигурацию для агрегации данных.
    
    Args:
        df: DataFrame с данными
        metrics_config: Конфигурация метрик {название: колонка}
        
    Returns:
        Dict[str, Tuple[str, str]]: Конфигурация для pandas.agg()
    """
    if metrics_config is None:
        metrics_config = {
            'cnt_order': 'id_order',
            'cnt_offer': 'offer_time',
            'cnt_assign': 'assign_time',
            'cnt_arrive': 'arrive_time',
            'cnt_trip': 'trip_time'
        }
    
    agg_dict = {}
    
    for metric_name, column in metrics_config.items():
        if column in df.columns:
            agg_dict[metric_name] = (column, 'count')
            print(f"✓ Метрика {metric_name} будет рассчитана по колонке {column}")
        else:
            print(f"⚠ Колонка {column} не найдена, пропускаем метрику {metric_name}")
    
    return agg_dict

def group_and_aggregate(df: pd.DataFrame,
                       group_by: Union[str, List[str]],
                       agg_config: Dict[str, Tuple[str, str]]) -> pd.DataFrame:
    """
    Группирует и агрегирует данные.
    
    Args:
        df: DataFrame с данными
        group_by: Колонки для группировки
        agg_config: Конфигурация агрегации
        
    Returns:
        pd.DataFrame: Агрегированные данные
    """
    print(f"Группировка по: {group_by}")
    print(f"Агрегация: {list(agg_config.keys())}")
    
    try:
        df_grouped = df.groupby(group_by, as_index=False).agg(agg_config)
        print(f"✓ Создано {len(df_grouped)} агрегированных записей")
        return df_grouped
    except Exception as e:
        raise ValueError(f"Ошибка при группировке: {e}")

def calculate_conversion(df: pd.DataFrame,
                        numerator_col: str,
                        denominator_col: str,
                        conversion_name: str) -> pd.Series:
    """
    Рассчитывает одну метрику конверсии.
    
    Args:
        df: DataFrame с данными
        numerator_col: Колонка числителя
        denominator_col: Колонка знаменателя
        conversion_name: Название конверсии
        
    Returns:
        pd.Series: Рассчитанная конверсия
    """
    if numerator_col not in df.columns:
        raise ValueError(f"Колонка {numerator_col} не найдена")
    if denominator_col not in df.columns:
        raise ValueError(f"Колонка {denominator_col} не найдена")
    
    # Избегаем деления на ноль
    conversion = df[numerator_col] / df[denominator_col].replace(0, np.nan)
    
    print(f"✓ Рассчитана конверсия {conversion_name}")
    return conversion

def calculate_all_conversions(df: pd.DataFrame) -> pd.DataFrame:
    """
    Рассчитывает все стандартные конверсии такси.
    
    Args:
        df: DataFrame с базовыми метриками
        
    Returns:
        pd.DataFrame: DataFrame с добавленными конверсиями
    """
    df_result = df.copy()
    
    # Определяем конверсии для расчета
    conversions = [
        ('cnt_trip', 'cnt_order', 'order2trip'),
        ('cnt_offer', 'cnt_order', 'order2offer'),
        ('cnt_assign', 'cnt_offer', 'offer2assign'),
        ('cnt_arrive', 'cnt_assign', 'assign2arrive'),
        ('cnt_trip', 'cnt_arrive', 'arrive2trip')
    ]
    
    print("Расчет конверсий:")
    
    for num_col, den_col, conv_name in conversions:
        try:
            df_result[conv_name] = calculate_conversion(df_result, num_col, den_col, conv_name)
        except ValueError as e:
            print(f"⚠ Пропускаем {conv_name}: {e}")
    
    # Обработка бесконечных значений
    df_result = df_result.replace([np.inf, -np.inf], np.nan)
    
    return df_result

def setup_plot_style(figsize: Tuple[int, int] = (12, 8),
                     style: str = 'default') -> None:
    """
    Настраивает стиль графика.
    
    Args:
        figsize: Размер фигуры
        style: Стиль matplotlib
    """
    plt.style.use(style)
    plt.figure(figsize=figsize)

def get_city_data(df: pd.DataFrame,
                 cities: Optional[List[str]] = None) -> Tuple[List[str], List[pd.DataFrame]]:
    """
    Получает данные для указанных городов.
    
    Args:
        df: DataFrame с данными
        cities: Список городов (если None, берутся все)
        
    Returns:
        Tuple[List[str], List[pd.DataFrame]]: Список городов и соответствующих данных
    """
    if 'city' not in df.columns:
        raise ValueError("Колонка 'city' не найдена в данных")
    
    available_cities = df['city'].unique().tolist()
    
    if cities is None:
        cities = available_cities
        print(f"Используются все доступные города: {cities}")
    else:
        # Проверяем доступность городов
        missing_cities = [city for city in cities if city not in available_cities]
        if missing_cities:
            print(f"⚠ Города не найдены в данных: {missing_cities}")
            cities = [city for city in cities if city in available_cities]
    
    if not cities:
        raise ValueError("Нет доступных городов для отображения")
    
    # Получаем данные для каждого города
    city_data_list = []
    for city in cities:
        city_data = df[df['city'] == city]
        if len(city_data) == 0:
            print(f"⚠ Нет данных для города: {city}")
        city_data_list.append(city_data)
    
    return cities, city_data_list

def plot_city_lines(cities: List[str],
                   city_data_list: List[pd.DataFrame],
                   x_column: str,
                   y_column: str) -> None:
    """
    Строит линии для каждого города на графике.
    
    Args:
        cities: Список названий городов
        city_data_list: Список DataFrame для каждого города
        x_column: Колонка для оси X
        y_column: Колонка для оси Y
    """
    colors = plt.cm.Set1(np.linspace(0, 1, len(cities)))
    
    for i, (city, city_data) in enumerate(zip(cities, city_data_list)):
        if len(city_data) == 0 or x_column not in city_data.columns or y_column not in city_data.columns:
            continue
            
        plt.plot(city_data[x_column], 
                city_data[y_column], 
                label=city, 
                color=colors[i],
                marker='o',
                linewidth=2,
                markersize=6)

def setup_plot_labels(title: Optional[str],
                      x_column: str,
                      y_column: str,
                      y_limit: Optional[Tuple[float, float]] = None) -> None:
    """
    Настраивает подписи и заголовки графика.
    
    Args:
        title: Заголовок графика
        x_column: Название колонки X
        y_column: Название колонки Y
        y_limit: Ограничения по оси Y
    """
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True, alpha=0.3)
    
    if title:
        plt.title(title, fontsize=14, fontweight='bold')
    else:
        plt.title(f"{y_column} по городам", fontsize=14, fontweight='bold')
    
    plt.xlabel(x_column.replace('_', ' ').title(), fontsize=12)
    plt.ylabel(y_column.replace('_', ' ').title(), fontsize=12)
    
    if y_limit:
        plt.ylim(y_limit)
    
    plt.tight_layout()

def plot_metric_by_cities(df: pd.DataFrame,
                         metric_column: str,
                         x_column: str = 'day_order',
                         cities: Optional[List[str]] = None,
                         title: Optional[str] = None,
                         y_limit: Optional[Tuple[float, float]] = None,
                         figsize: Tuple[int, int] = (12, 8),
                         style: str = 'default',
                         save_path: Optional[str] = None) -> None:
    """
    Строит график метрики по городам.
    
    Args:
        df: DataFrame с данными
        metric_column: Колонка с метрикой
        x_column: Колонка для оси X
        cities: Список городов
        title: Заголовок графика
        y_limit: Ограничения по оси Y
        figsize: Размер фигуры
        style: Стиль графика
        save_path: Путь для сохранения
    """
    # Проверка входных данных
    required_cols = [metric_column, x_column]
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"Отсутствуют колонки: {missing_cols}")
    
    # Настройка графика
    setup_plot_style(figsize, style)
    
    # Получение данных по городам
    cities, city_data_list = get_city_data(df, cities)
    
    # Построение линий
    plot_city_lines(cities, city_data_list, x_column, metric_column)
    
    # Настройка подписей
    setup_plot_labels(title, x_column, metric_column, y_limit)
    
    # Сохранение
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"График сохранен: {save_path}")
    
    plt.show()

def detect_outliers_iqr(data: pd.Series, threshold: float = 1.5) -> pd.Series:
    """
    Детекция выбросов методом межквартильного размаха (IQR).
    
    Args:
        data: Серия данных для анализа
        threshold: Пороговое значение (обычно 1.5)
        
    Returns:
        pd.Series: Булева маска выбросов
    """
    Q1 = data.quantile(0.25)
    Q3 = data.quantile(0.75)
    IQR = Q3 - Q1
    
    lower_bound = Q1 - threshold * IQR
    upper_bound = Q3 + threshold * IQR
    
    return (data < lower_bound) | (data > upper_bound)

def detect_outliers_zscore(data: pd.Series, threshold: float = 3.0) -> pd.Series:
    """
    Детекция выбросов методом Z-score.
    
    Args:
        data: Серия данных для анализа
        threshold: Пороговое значение Z-score
        
    Returns:
        pd.Series: Булева маска выбросов
    """
    z_scores = np.abs(stats.zscore(data.dropna()))
    outlier_mask = pd.Series(False, index=data.index)
    outlier_mask.loc[data.dropna().index] = z_scores > threshold
    
    return outlier_mask

def analyze_column_outliers(df: pd.DataFrame,
                           column: str,
                           method: str = 'iqr',
                           threshold: float = 1.5) -> Dict[str, Any]:
    """
    Анализирует выбросы в одной колонке.
    
    Args:
        df: DataFrame с данными
        column: Название колонки
        method: Метод детекции ('iqr' или 'zscore')
        threshold: Пороговое значение
        
    Returns:
        Dict[str, Any]: Результаты анализа
    """
    if column not in df.columns:
        raise ValueError(f"Колонка {column} не найдена")
    
    data = df[column].dropna()
    if len(data) == 0:
        return {'column': column, 'outliers_count': 0, 'outliers_percentage': 0.0}
    
    # Выбор метода детекции
    if method == 'iqr':
        outlier_mask = detect_outliers_iqr(data, threshold)
    elif method == 'zscore':
        outlier_mask = detect_outliers_zscore(data, threshold)
    else:
        raise ValueError(f"Неподдерживаемый метод: {method}")
    
    outliers_count = outlier_mask.sum()
    outliers_percentage = (outliers_count / len(data)) * 100
    
    print(f"Колонка {column}: {outliers_count} выбросов ({outliers_percentage:.1f}%)")
    
    return {
        'column': column,
        'outliers_count': outliers_count,
        'outliers_percentage': outliers_percentage,
        'method': method,
        'threshold': threshold,
        'outlier_indices': data[outlier_mask].index.tolist()
    }

def find_conversion_anomalies(df: pd.DataFrame,
                             conversion_column: str,
                             group_column: str = 'city',
                             low_threshold: float = 0.1,
                             high_threshold: float = 0.9) -> Dict[str, List[Dict]]:
    """
    Находит аномалии в конверсиях по группам.
    
    Args:
        df: DataFrame с данными
        conversion_column: Колонка с конверсией
        group_column: Колонка для группировки
        low_threshold: Порог низких конверсий
        high_threshold: Порог высоких конверсий
        
    Returns:
        Dict[str, List[Dict]]: Найденные аномалии
    """
    if conversion_column not in df.columns:
        raise ValueError(f"Колонка {conversion_column} не найдена")
    if group_column not in df.columns:
        raise ValueError(f"Колонка {group_column} не найдена")
    
    anomalies = {
        'low_conversion': [],
        'high_conversion': [],
        'zero_conversion': []
    }
    
    for group_value in df[group_column].unique():
        group_data = df[df[group_column] == group_value][conversion_column].dropna()
        
        if len(group_data) == 0:
            continue
        
        low_count = (group_data < low_threshold).sum()
        high_count = (group_data > high_threshold).sum()
        zero_count = (group_data == 0).sum()
        
        if low_count > 0:
            anomalies['low_conversion'].append({
                'group': group_value,
                'count': low_count,
                'percentage': (low_count / len(group_data)) * 100
            })
        
        if high_count > 0:
            anomalies['high_conversion'].append({
                'group': group_value,
                'count': high_count,
                'percentage': (high_count / len(group_data)) * 100
            })
        
        if zero_count > 0:
            anomalies['zero_conversion'].append({
                'group': group_value,
                'count': zero_count,
                'percentage': (zero_count / len(group_data)) * 100
            })
    
    return anomalies

def perform_complete_analysis(file_path: str,
                             required_columns: Optional[List[str]] = None,
                             time_columns: Optional[List[str]] = None,
                             group_by: Union[str, List[str]] = ['day_order', 'city']) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Выполняет полный анализ данных такси, используя модульные функции.
    
    Args:
        file_path: Путь к файлу с данными
        required_columns: Обязательные колонки
        time_columns: Временные колонки
        group_by: Колонки для группировки
        
    Returns:
        Tuple[pd.DataFrame, pd.DataFrame]: Исходные данные и агрегированные метрики
    """
    print("=== НАЧАЛО ПОЛНОГО АНАЛИЗА ===")
    
    # 1. Загрузка данных
    print("\n1. Загрузка данных")
    df = load_data_file(file_path)
    
    # 2. Валидация
    print("\n2. Валидация данных")
    if required_columns is None:
        required_columns = ['id_order', 'order_time', 'offer_time', 'assign_time', 
                          'arrive_time', 'trip_time', 'city']
    
    validate_required_columns(df, required_columns)
    check_duplicates(df)
    analyze_missing_values(df)
    
    # 3. Обработка времени
    print("\n3. Обработка временных данных")
    if time_columns is None:
        time_columns = ['order_time', 'offer_time', 'assign_time', 'arrive_time', 'trip_time']
    
    df = convert_time_columns(df, time_columns)
    df = add_time_derived_columns(df)
    
    # 4. Расчет метрик
    print("\n4. Расчет метрик")
    agg_config = create_aggregation_config(df)
    df_metrics = group_and_aggregate(df, group_by, agg_config)
    df_metrics = calculate_all_conversions(df_metrics)
    
    print("\n=== АНАЛИЗ ЗАВЕРШЕН ===")
    return df, df_metrics

# Выполняем полный анализ одной функцией
df_original, df_metrics = perform_complete_analysis('taxi_data.xlsx')

print("\nРезультаты анализа:")
print(f"Исходные данные: {df_original.shape}")
print(f"Агрегированные метрики: {df_metrics.shape}")
print(f"Колонки метрик: {list(df_metrics.columns)}")

df_metrics.head()

# График: Количество заказов
plot_metric_by_cities(df_metrics,
                     metric_column='cnt_order',
                     title="Количество заказов",
                     cities=['Казань', 'Москва', 'Санкт-Петербург', 'Краснодар'])

# График: Order2Trip - Базовая конверсия
plot_metric_by_cities(df_metrics,
                     metric_column='order2trip',
                     title="Order2Trip - Базовая конверсия",
                     y_limit=(0, 1),
                     cities=['Казань', 'Москва', 'Санкт-Петербург', 'Краснодар'])

# График: Order2Offer - Конверсия из заказа в предложение
plot_metric_by_cities(df_metrics,
                     metric_column='order2offer',
                     title="Order2Offer - Конверсия из заказа в предложение",
                     y_limit=(0, 1),
                     cities=['Казань', 'Москва', 'Санкт-Петербург', 'Краснодар'])

# Анализ выбросов в исходных данных
print("=== АНАЛИЗ ВЫБРОСОВ ===")

# Анализируем числовые колонки
numeric_columns = ['day_order']

for column in numeric_columns:
    if column in df_original.columns:
        outlier_results = analyze_column_outliers(df_original, column, method='iqr')
        print(f"Результаты для {column}: {outlier_results['outliers_count']} выбросов")

# Анализ аномалий в конверсиях
print("\n=== АНАЛИЗ АНОМАЛИЙ В КОНВЕРСИЯХ ===")

conversion_columns = ['order2trip', 'order2offer', 'offer2assign']

for conv_col in conversion_columns:
    if conv_col in df_metrics.columns:
        anomalies = find_conversion_anomalies(df_metrics, conv_col)
        
        print(f"\nАномалии в {conv_col}:")
        if anomalies['low_conversion']:
            print(f"  Низкие конверсии: {len(anomalies['low_conversion'])} групп")
        if anomalies['high_conversion']:
            print(f"  Высокие конверсии: {len(anomalies['high_conversion'])} групп")
        if anomalies['zero_conversion']:
            print(f"  Нулевые конверсии: {len(anomalies['zero_conversion'])} групп")